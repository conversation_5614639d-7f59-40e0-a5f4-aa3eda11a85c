# 📈 Dashboard - Análise Temporal da Carteira de Investimentos

Este dashboard interativo foi criado com **Streamlit** e **Plotly** para visualizar e analisar a evolução temporal da carteira de investimentos baseada no script `analise_carteira_temporal.py`.

## 🚀 Como Executar o Dashboard

### 1. Pré-requisitos
Certifique-se de que as dependências estão instaladas:
```bash
uv add streamlit plotly
```

### 2. Executar a Análise Temporal (se necessário)
Primeiro, execute a análise temporal para gerar os dados:
```bash
uv run python src/analise_carteira_temporal.py
```

### 3. Iniciar o Dashboard
Execute o dashboard:
```bash
uv run streamlit run dashboard_carteira.py
```

O dashboard será aberto automaticamente no navegador em `http://localhost:8501`

## 📊 Funcionalidades do Dashboard

### 🎯 Resumo Executivo
- **Capital Inicial**: Valor inicial investido
- **Capital Total Atual**: Valor total atual da carteira
- **Rendimento (%)**: Percentual de rendimento da carteira
- **Capital Disponível**: Valor disponível para novos investimentos

### 📈 Gráficos Interativos

#### 1. Evolução Temporal do Capital
- **Gráfico Principal**: Mostra a evolução do capital inicial, disponível, valor das ações e capital total
- **Gráfico de Rendimento**: Exibe o rendimento percentual ao longo do tempo
- **Interatividade**: Hover para ver detalhes, zoom, pan

#### 2. Análise de Transações
- **Resumo de Transações**: Total de compras e vendas
- **Ações Mais Negociadas**: Ranking das ações por quantidade
- **Distribuição por Ação**: Gráfico de pizza das posições atuais

#### 3. Timeline de Transações
- **Visualização Temporal**: Scatter plot mostrando todas as transações ao longo do tempo
- **Diferenciação**: Compras em verde, vendas em vermelho
- **Tamanho**: Proporcional ao valor da transação

### ⚙️ Controles Interativos

#### Filtros na Sidebar
- **📅 Filtros de Data**: Selecione período específico para análise
- **🔄 Atualizar Análise**: Botão para reprocessar os dados

#### Abas de Dados Detalhados
- **📈 Evolução Temporal**: Tabela com todos os dados da evolução
- **💼 Transações**: Histórico completo de transações formatado

## 🎨 Características Visuais

### Cores e Estilo
- **Verde**: Lucros, compras, tendências positivas
- **Vermelho**: Prejuízos, vendas, tendências negativas
- **Azul**: Capital disponível
- **Laranja**: Valor das ações
- **Roxo**: Rendimento percentual

### Responsividade
- Layout adaptável para diferentes tamanhos de tela
- Gráficos redimensionáveis
- Interface otimizada para desktop e tablet

## 📋 Estrutura dos Dados

### Dados de Entrada
- **carteira.csv**: Arquivo com transações da carteira
- **results/evolucao_carteira_temporal.csv**: Dados da evolução temporal

### Colunas Principais
- `data`: Data da análise
- `capital_inicial`: Capital inicial
- `capital_disponivel`: Capital disponível
- `valor_atual_acoes`: Valor atual das ações
- `capital_total`: Capital total
- `rendimento_capital_percentual`: Rendimento em %

## 🔧 Personalização

### Modificar Período de Análise
Use os filtros de data na sidebar para focar em períodos específicos.

### Atualizar Dados
1. Clique em "🔄 Atualizar Análise" na sidebar
2. Ou execute novamente `analise_carteira_temporal.py`

### Adicionar Novas Métricas
Edite a função `criar_dashboard()` em `analise_carteira_temporal.py` para adicionar:
- Novas métricas no resumo executivo
- Gráficos adicionais
- Filtros personalizados

## 🚨 Solução de Problemas

### Dashboard não carrega
1. Verifique se `results/evolucao_carteira_temporal.csv` existe
2. Execute primeiro `analise_carteira_temporal.py`
3. Verifique se todas as dependências estão instaladas

### Dados não aparecem
1. Verifique se `carteira.csv` existe e tem dados válidos
2. Confirme se as datas estão no formato correto
3. Execute a análise temporal novamente

### Gráficos não interativos
1. Verifique se Plotly está instalado: `uv add plotly`
2. Limpe o cache do Streamlit: Ctrl+C e reinicie

## 📁 Arquivos Relacionados

- `dashboard_carteira.py`: Script principal do dashboard
- `src/analise_carteira_temporal.py`: Análise temporal e função do dashboard
- `carteira.csv`: Dados da carteira
- `config.yaml`: Configurações do sistema
- `results/evolucao_carteira_temporal.csv`: Dados processados

## 💡 Dicas de Uso

1. **Análise de Períodos**: Use os filtros de data para analisar períodos específicos
2. **Hover nos Gráficos**: Passe o mouse sobre os pontos para ver detalhes
3. **Zoom**: Use as ferramentas de zoom do Plotly para focar em áreas específicas
4. **Exportar Dados**: Os dados podem ser copiados das tabelas para análises externas
5. **Atualização Automática**: O dashboard detecta automaticamente mudanças nos dados

## 🔄 Atualizações Futuras

Possíveis melhorias:
- Comparação com benchmarks (Ibovespa, CDI)
- Análise de risco (volatilidade, drawdown)
- Projeções futuras
- Alertas automáticos
- Exportação de relatórios em PDF

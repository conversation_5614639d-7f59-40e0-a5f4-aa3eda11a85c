#!/usr/bin/env python3
"""
Análise Temporal da Carteira de Investimentos

Este script gera análise temporal da carteira com:
- Gráficos temporais individuais para cada ação
- Gráfico temporal do rendimento total da carteira
- Evolução do valor investido vs valor atual ao longo do tempo

Baseado na estrutura do analise_carteira_simples.py mas com foco temporal.
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
import os
import sys
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import ConfigLoader

# Configurar estilo dos gráficos
plt.style.use('default')
sns.set_palette("husl")

def corrigir_valores_tims3(dados, ticker):
    """Aplica correção específica para TIMS3 até 02/07/2025"""
    if ticker != 'TIMS3.SA':
        return dados

    # Data limite para correção (timezone-naive)
    data_limite = pd.to_datetime('2025-07-02').tz_localize(None)
    colunas_preco = ['Open', 'High', 'Low', 'Close', 'Adj Close']

    # Verificar se o índice é datetime
    if not isinstance(dados.index, pd.DatetimeIndex):
        return dados

    # Garantir que o índice seja timezone-naive
    dados_index = dados.index
    if dados_index.tz is not None:
        dados_index = dados_index.tz_convert(None)
        dados.index = dados_index

    # Filtrar dados até a data limite
    mask_correcao = dados_index <= data_limite

    if mask_correcao.any():
        print(f"     🔧 Corrigindo valores TIMS3 até 02/07/2025 (dividindo por 100)")

        # Aplicar correção nas colunas de preço
        for coluna in colunas_preco:
            if coluna in dados.columns:
                dados.loc[mask_correcao, coluna] = dados.loc[mask_correcao, coluna] / 100

        print(f"     ✅ Correção aplicada em {mask_correcao.sum()} registros")

    return dados

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    carteira = pd.read_csv(arquivo_csv)
    carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
    return carteira

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações desde a data de início"""
    print("Obtendo dados históricos...")
    dados_historicos = {}
    
    # Calcular data final (hoje + margem)
    data_fim = datetime.now() + timedelta(days=1)
    
    for ticker in tickers:
        try:
            print(f"  Baixando dados para {ticker}...")
            stock = yf.Ticker(ticker)
            hist = stock.history(start=data_inicio, end=data_fim)
            
            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                dados_historicos[ticker] = hist
                print(f"  ✓ {len(hist)} registros obtidos para {ticker}")
            else:
                print(f"  ✗ Nenhum dado obtido para {ticker}")
                
        except Exception as e:
            print(f"  ✗ Erro ao obter dados para {ticker}: {e}")
    
    return dados_historicos

def processar_transacoes_temporais(carteira):
    """Processa transações da carteira organizadas temporalmente"""
    # Ordenar por data
    carteira_ordenada = carteira.sort_values('data_compra').copy()
    
    # Criar estrutura para acompanhar posições ao longo do tempo
    transacoes = []
    
    for _, transacao in carteira_ordenada.iterrows():
        ticker = transacao['ticker']
        quantidade = transacao['quantidade']
        data = transacao['data_compra']
        preco = transacao['preco_compra']
        
        transacoes.append({
            'ticker': ticker,
            'data': data,
            'quantidade': quantidade,
            'preco': preco,
            'valor_transacao': quantidade * preco,
            'tipo': 'COMPRA' if quantidade > 0 else 'VENDA'
        })
    
    return pd.DataFrame(transacoes)

def obter_precos_atuais_para_temporal(tickers):
    """Obtém preços atuais das ações para o último dia da análise temporal"""
    print("  Obtendo preços atuais para o último dia...")
    precos = {}

    for ticker in tickers:
        try:
            stock = yf.Ticker(ticker)
            # Primeiro tenta obter dados do dia atual
            hist = stock.history(period='1d')

            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                precos[ticker] = hist['Close'].iloc[-1]
            else:
                # Se não há dados do dia atual, busca dados dos últimos 5 dias
                hist = stock.history(period='5d')
                if not hist.empty:
                    hist = corrigir_valores_tims3(hist, ticker)
                    precos[ticker] = hist['Close'].iloc[-1]
        except Exception as e:
            print(f"    ⚠️ Erro ao obter preço atual para {ticker}: {e}")

    return precos

def calcular_posicoes_temporais(transacoes_df, dados_historicos):
    """Calcula evolução das posições ao longo do tempo"""
    # Obter todas as datas únicas dos dados históricos
    todas_datas = set()
    for ticker, dados in dados_historicos.items():
        # Converter índice para timezone-naive se necessário
        if dados.index.tz is not None:
            dados.index = dados.index.tz_convert(None)
        todas_datas.update(dados.index.date)

    todas_datas = sorted(todas_datas)

    # Converter para datetime timezone-naive
    todas_datas = [pd.to_datetime(data).tz_localize(None) for data in todas_datas]

    # Filtrar apenas datas a partir da primeira transação
    data_inicio = transacoes_df['data'].min()
    if data_inicio.tz is not None:
        data_inicio = data_inicio.tz_localize(None)
    todas_datas = [data for data in todas_datas if data >= data_inicio]

    # Para o último dia, obter preços atuais mais precisos
    data_mais_recente = max(todas_datas)
    tickers_ativos = transacoes_df['ticker'].unique()
    precos_atuais_precisos = obter_precos_atuais_para_temporal(tickers_ativos)
    
    # Estrutura para armazenar evolução temporal
    evolucao_temporal = []
    
    # Para cada data, calcular posições e valores
    for data_atual in todas_datas:
        # Filtrar transações até a data atual
        transacoes_ate_data = transacoes_df[transacoes_df['data'] <= data_atual]
        
        if transacoes_ate_data.empty:
            continue
        
        # Calcular posições consolidadas até esta data
        posicoes_data = {}
        valor_total_investido = 0
        valor_total_atual = 0
        
        # Consolidar posições por ticker
        for ticker in transacoes_ate_data['ticker'].unique():
            transacoes_ticker = transacoes_ate_data[transacoes_ate_data['ticker'] == ticker]
            
            quantidade_total = transacoes_ticker['quantidade'].sum()
            valor_investido_bruto = transacoes_ticker[transacoes_ticker['quantidade'] > 0]['valor_transacao'].sum()
            valor_vendido = abs(transacoes_ticker[transacoes_ticker['quantidade'] < 0]['valor_transacao'].sum())
            valor_investido_liquido = valor_investido_bruto - valor_vendido
            
            # Obter preço atual para esta data
            preco_atual = None

            # Se é o último dia e temos preços atuais mais precisos, usar eles
            if data_atual == data_mais_recente and ticker in precos_atuais_precisos:
                preco_atual = precos_atuais_precisos[ticker]
            elif ticker in dados_historicos:
                dados_ticker = dados_historicos[ticker].copy()
                # Garantir que o índice seja timezone-naive
                if dados_ticker.index.tz is not None:
                    dados_ticker.index = dados_ticker.index.tz_convert(None)

                # Garantir que data_atual seja timezone-naive
                data_atual_naive = data_atual
                if hasattr(data_atual, 'tz') and data_atual.tz is not None:
                    data_atual_naive = data_atual.tz_localize(None)

                # Encontrar preço mais próximo da data atual
                datas_disponiveis = dados_ticker.index[dados_ticker.index <= data_atual_naive]
                if not datas_disponiveis.empty:
                    data_mais_proxima = datas_disponiveis.max()
                    preco_atual = dados_ticker.loc[data_mais_proxima, 'Close']
            
            if preco_atual is not None and quantidade_total > 0:
                valor_atual_ticker = quantidade_total * preco_atual
                
                posicoes_data[ticker] = {
                    'quantidade': quantidade_total,
                    'valor_investido_bruto': valor_investido_bruto,
                    'valor_investido_liquido': valor_investido_liquido,
                    'valor_atual': valor_atual_ticker,
                    'preco_atual': preco_atual
                }
                
                valor_total_investido += valor_investido_liquido
                valor_total_atual += valor_atual_ticker
        
        # Calcular totais seguindo EXATAMENTE a lógica da análise simples
        # Valor total investido bruto (soma de TODAS as compras, incluindo as vendidas)
        valor_total_investido_bruto = 0
        valor_total_vendido = 0

        for ticker in transacoes_ate_data['ticker'].unique():
            transacoes_ticker = transacoes_ate_data[transacoes_ate_data['ticker'] == ticker]

            # Somar TODAS as compras (quantidade > 0)
            compras_ticker = transacoes_ticker[transacoes_ticker['quantidade'] > 0]['valor_transacao'].sum()
            valor_total_investido_bruto += compras_ticker

            # Somar TODAS as vendas (quantidade < 0)
            vendas_ticker = transacoes_ticker[transacoes_ticker['quantidade'] < 0]['valor_transacao'].sum()
            valor_total_vendido += abs(vendas_ticker)

        # Calcular valor_total_recuperavel (valor atual das ações + vendas)
        valor_total_recuperavel = valor_total_atual + valor_total_vendido

        # Calcular capital seguindo EXATAMENTE a lógica da análise simples
        config_loader = ConfigLoader()
        capital_inicial = config_loader.get_initial_capital('portfolio_analysis')
        capital_disponivel = capital_inicial - valor_total_investido_bruto + valor_total_vendido
        capital_total = capital_disponivel + valor_total_recuperavel - valor_total_vendido

        # Rendimento do capital (equivalente ao gráfico de barras da análise simples)
        rendimento_capital_absoluto = capital_total - capital_inicial
        rendimento_capital_percentual = (rendimento_capital_absoluto / capital_inicial) * 100

        evolucao_temporal.append({
            'data': data_atual,
            'valor_investido_liquido': valor_total_investido,  # Valor líquido investido
            'valor_investido_bruto': valor_total_investido_bruto,  # Valor bruto investido
            'valor_atual_total': valor_total_atual,  # Valor atual das ações
            'valor_vendido_total': valor_total_vendido,  # Valor total das vendas
            'valor_total_recuperavel': valor_total_recuperavel,  # Valor atual + vendas
            'capital_inicial': capital_inicial,
            'capital_disponivel': capital_disponivel,
            'capital_total': capital_total,
            'rendimento_capital_absoluto': rendimento_capital_absoluto,
            'rendimento_capital_percentual': rendimento_capital_percentual,
            'posicoes': posicoes_data.copy()
        })
    
    return evolucao_temporal

def gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos):
    """Gera gráficos temporais individuais para cada ação"""
    print("Gerando gráficos temporais individuais...")
    
    # Obter todos os tickers únicos
    todos_tickers = set()
    for snapshot in evolucao_temporal:
        todos_tickers.update(snapshot['posicoes'].keys())
    
    # Criar diretório para gráficos individuais
    os.makedirs('results/figures/temporal_individual', exist_ok=True)
    
    for ticker in todos_tickers:
        # Coletar dados temporais para este ticker
        datas = []
        valores_investidos = []
        valores_atuais = []
        quantidades = []
        precos = []
        
        for snapshot in evolucao_temporal:
            if ticker in snapshot['posicoes']:
                posicao = snapshot['posicoes'][ticker]
                datas.append(snapshot['data'])
                valores_investidos.append(posicao['valor_investido_liquido'])
                valores_atuais.append(posicao['valor_atual'])
                quantidades.append(posicao['quantidade'])
                precos.append(posicao['preco_atual'])
        
        if not datas:
            continue
        
        # Criar gráfico para este ticker
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Gráfico 1: Evolução do valor investido vs atual
        ax1.plot(datas, valores_investidos, label='Valor Investido', linewidth=2, color='blue')
        ax1.plot(datas, valores_atuais, label='Valor Atual', linewidth=2, color='green')
        ax1.fill_between(datas, valores_investidos, valores_atuais, alpha=0.3, 
                        color='green' if valores_atuais[-1] > valores_investidos[-1] else 'red')
        
        ax1.set_title(f'{ticker.replace(".SA", "")} - Evolução do Investimento\n'
                     f'Quantidade: {quantidades[-1]} ações', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Valor (R$)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Gráfico 2: Evolução do preço da ação
        if ticker in dados_historicos:
            dados_ticker = dados_historicos[ticker]
            # Filtrar dados para o período relevante
            dados_periodo = dados_ticker[dados_ticker.index >= datas[0]]
            
            ax2.plot(dados_periodo.index, dados_periodo['Close'], 
                    label='Preço de Fechamento', linewidth=1.5, color='orange')
            
            # Marcar pontos de compra/venda
            # Aqui você pode adicionar marcadores para transações específicas
            
        ax2.set_title(f'Evolução do Preço da Ação', fontsize=12)
        ax2.set_xlabel('Data', fontsize=12)
        ax2.set_ylabel('Preço (R$)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # Salvar gráfico
        nome_arquivo = f'temporal_{ticker.replace(".SA", "")}.png'
        caminho_arquivo = f'results/figures/temporal_individual/{nome_arquivo}'
        plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  ✓ Gráfico salvo: {caminho_arquivo}")

def gerar_grafico_carteira_total(evolucao_temporal):
    """Gera gráfico da evolução temporal da carteira total"""
    print("Gerando gráfico da carteira total...")

    # Extrair dados temporais seguindo a lógica da análise simples
    datas = [snapshot['data'] for snapshot in evolucao_temporal]
    capital_inicial = [snapshot['capital_inicial'] for snapshot in evolucao_temporal]
    capital_disponivel = [snapshot['capital_disponivel'] for snapshot in evolucao_temporal]
    valor_atual_acoes = [snapshot['valor_atual_total'] for snapshot in evolucao_temporal]
    capital_total = [snapshot['capital_total'] for snapshot in evolucao_temporal]
    rendimento_capital_pct = [snapshot['rendimento_capital_percentual'] for snapshot in evolucao_temporal]
    
    # Criar gráfico
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # Gráfico 1: Evolução do Capital Total (equivalente ao gráfico de barras da análise simples)
    ax1.axhline(y=capital_inicial[0], color='black', linestyle='--', alpha=0.7,
                label=f'Capital Inicial (R$ {capital_inicial[0]:.2f})')

    ax1.plot(datas, capital_disponivel, label='Capital Disponível',
             linewidth=2.5, color='blue', marker='o', markersize=3)
    ax1.plot(datas, valor_atual_acoes, label='Valor Atual das Ações',
             linewidth=2.5, color='orange', marker='s', markersize=3)
    ax1.plot(datas, capital_total, label='Capital Total',
             linewidth=3, color='green', marker='D', markersize=4)

    # Preencher área entre capital inicial e capital total
    ax1.fill_between(datas, capital_inicial, capital_total, alpha=0.3,
                    color='green' if capital_total[-1] > capital_inicial[0] else 'red')

    ax1.set_title('Evolução Temporal do Capital Total da Carteira\n(Equivalente ao Gráfico de Barras da Análise Simples)',
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # Adicionar anotações de valores finais
    capital_final = capital_total[-1]
    capital_disponivel_final = capital_disponivel[-1]
    valor_acoes_final = valor_atual_acoes[-1]

    ax1.annotate(f'Capital Total: R$ {capital_final:.2f}',
                xy=(datas[-1], capital_final),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                color='white', fontweight='bold')

    ax1.annotate(f'Disponível: R$ {capital_disponivel_final:.2f}',
                xy=(datas[-1], capital_disponivel_final),
                xytext=(10, -20), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7),
                color='white', fontweight='bold')
    
    # Gráfico 2: Evolução do rendimento percentual do capital
    ax2.plot(datas, rendimento_capital_pct, linewidth=2.5, color='purple', marker='D', markersize=4)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.fill_between(datas, 0, rendimento_capital_pct, alpha=0.3,
                    color='green' if rendimento_capital_pct[-1] >= 0 else 'red')

    ax2.set_title('Evolução do Rendimento Percentual do Capital', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Rendimento do Capital (%)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)

    # Adicionar anotação do rendimento final
    rendimento_final_pct = rendimento_capital_pct[-1]
    ax2.annotate(f'{rendimento_final_pct:.2f}%',
                xy=(datas[-1], rendimento_final_pct),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3',
                         facecolor='green' if rendimento_final_pct >= 0 else 'red', alpha=0.7),
                color='white', fontweight='bold')
    
    plt.tight_layout()
    
    # Salvar gráfico
    caminho_arquivo = 'results/figures/evolucao_carteira_temporal.png'
    plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  ✓ Gráfico da carteira total salvo: {caminho_arquivo}")
    
    # Imprimir resumo final seguindo a lógica da análise simples
    print(f"\n{'='*60}")
    print(f"RESUMO FINAL DA ANÁLISE TEMPORAL")
    print(f"{'='*60}")
    print(f"Período analisado: {datas[0].strftime('%d/%m/%Y')} a {datas[-1].strftime('%d/%m/%Y')}")
    print(f"Capital Inicial: R$ {capital_inicial[0]:.2f}")
    print(f"Capital Disponível: R$ {capital_disponivel_final:.2f}")
    print(f"Valor Atual das Ações: R$ {valor_acoes_final:.2f}")
    print(f"Capital Total: R$ {capital_final:.2f}")

    rendimento_capital_absoluto_final = capital_final - capital_inicial[0]
    print(f"Rendimento do Capital: R$ {rendimento_capital_absoluto_final:.2f} ({rendimento_final_pct:.2f}%)")

    status = "📈 LUCRO" if rendimento_capital_absoluto_final >= 0 else "📉 PREJUÍZO"
    print(f"Status: {status}")

    print(f"\n💡 NOTA: Valores equivalentes ao gráfico de barras da análise simples")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("="*60)
    print("ANÁLISE TEMPORAL DA CARTEIRA DE INVESTIMENTOS")
    print("="*60)
    
    # Carregar carteira
    print("1. Carregando dados da carteira...")
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None or carteira.empty:
        print("❌ Erro: Não foi possível carregar a carteira")
        return
    
    print(f"   ✓ Carteira carregada: {len(carteira)} transações")
    
    # Processar transações temporalmente
    print("2. Processando transações temporais...")
    transacoes_df = processar_transacoes_temporais(carteira)
    print(f"   ✓ {len(transacoes_df)} transações processadas")
    
    # Obter dados históricos
    print("3. Obtendo dados históricos...")
    tickers = carteira['ticker'].unique()
    data_inicio = carteira['data_compra'].min() - timedelta(days=1)
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("❌ Erro: Nenhum dado histórico foi obtido")
        return
    
    print(f"   ✓ Dados históricos obtidos para {len(dados_historicos)} tickers")
    
    # Calcular evolução temporal das posições
    print("4. Calculando evolução temporal das posições...")
    evolucao_temporal = calcular_posicoes_temporais(transacoes_df, dados_historicos)
    print(f"   ✓ Evolução calculada para {len(evolucao_temporal)} períodos")
    
    # Criar diretório para resultados
    os.makedirs('results/figures', exist_ok=True)
    
    # Gerar gráficos individuais
    print("5. Gerando gráficos temporais individuais...")
    gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos)
    
    # Gerar gráfico da carteira total
    print("6. Gerando gráfico da carteira total...")
    gerar_grafico_carteira_total(evolucao_temporal)
    
    # Salvar dados da evolução temporal
    print("7. Salvando dados da evolução temporal...")
    os.makedirs('results', exist_ok=True)
    
    # Converter evolução temporal para DataFrame
    dados_evolucao = []
    for snapshot in evolucao_temporal:
        dados_evolucao.append({
            'data': snapshot['data'],
            'capital_inicial': snapshot['capital_inicial'],
            'capital_disponivel': snapshot['capital_disponivel'],
            'valor_atual_acoes': snapshot['valor_atual_total'],
            'valor_total_recuperavel': snapshot['valor_total_recuperavel'],
            'capital_total': snapshot['capital_total'],
            'valor_investido_bruto': snapshot['valor_investido_bruto'],
            'valor_vendido_total': snapshot['valor_vendido_total'],
            'rendimento_capital_absoluto': snapshot['rendimento_capital_absoluto'],
            'rendimento_capital_percentual': snapshot['rendimento_capital_percentual']
        })
    
    df_evolucao = pd.DataFrame(dados_evolucao)
    df_evolucao.to_csv('results/evolucao_carteira_temporal.csv', index=False)
    print(f"   ✓ Dados salvos em 'results/evolucao_carteira_temporal.csv'")
    
    print("\n✅ Análise temporal concluída com sucesso!")
    print(f"📁 Gráficos salvos em: results/figures/")
    print(f"📊 Gráficos individuais em: results/figures/temporal_individual/")

def criar_dashboard():
    """Cria dashboard interativo com Streamlit"""
    import streamlit as st
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots

    # Configuração da página
    st.set_page_config(
        page_title="Dashboard - Análise Temporal da Carteira",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Título principal
    st.title("📈 Dashboard - Análise Temporal da Carteira de Investimentos")
    st.markdown("---")

    # Sidebar para controles
    st.sidebar.header("⚙️ Configurações")

    # Verificar se existem dados
    arquivo_evolucao = 'results/evolucao_carteira_temporal.csv'
    arquivo_carteira = 'carteira.csv'

    if not os.path.exists(arquivo_evolucao):
        st.error("❌ Dados da evolução temporal não encontrados. Execute primeiro a análise temporal.")
        if st.button("🔄 Executar Análise Temporal"):
            with st.spinner("Executando análise temporal..."):
                main()
            st.rerun()
        return

    # Carregar dados
    @st.cache_data
    def carregar_dados():
        df_evolucao = pd.read_csv(arquivo_evolucao)
        df_evolucao['data'] = pd.to_datetime(df_evolucao['data'])

        carteira = pd.read_csv(arquivo_carteira)
        carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])

        return df_evolucao, carteira

    try:
        df_evolucao, carteira = carregar_dados()
    except Exception as e:
        st.error(f"❌ Erro ao carregar dados: {e}")
        return

    # Filtros na sidebar
    st.sidebar.subheader("📅 Filtros de Data")
    data_inicio = st.sidebar.date_input(
        "Data Inicial",
        value=df_evolucao['data'].min().date(),
        min_value=df_evolucao['data'].min().date(),
        max_value=df_evolucao['data'].max().date()
    )

    data_fim = st.sidebar.date_input(
        "Data Final",
        value=df_evolucao['data'].max().date(),
        min_value=df_evolucao['data'].min().date(),
        max_value=df_evolucao['data'].max().date()
    )

    # Filtrar dados por data
    mask_data = (df_evolucao['data'].dt.date >= data_inicio) & (df_evolucao['data'].dt.date <= data_fim)
    df_filtrado = df_evolucao[mask_data].copy()

    if df_filtrado.empty:
        st.warning("⚠️ Nenhum dado encontrado para o período selecionado.")
        return

    # Métricas principais
    st.header("📊 Resumo Executivo")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        capital_inicial = df_filtrado['capital_inicial'].iloc[0]
        st.metric(
            label="💰 Capital Inicial",
            value=f"R$ {capital_inicial:,.2f}"
        )

    with col2:
        capital_final = df_filtrado['capital_total'].iloc[-1]
        rendimento_abs = capital_final - capital_inicial
        st.metric(
            label="💼 Capital Total Atual",
            value=f"R$ {capital_final:,.2f}",
            delta=f"R$ {rendimento_abs:,.2f}"
        )

    with col3:
        rendimento_pct = df_filtrado['rendimento_capital_percentual'].iloc[-1]
        st.metric(
            label="📈 Rendimento (%)",
            value=f"{rendimento_pct:.2f}%",
            delta=f"{rendimento_pct:.2f}%"
        )

    with col4:
        valor_acoes = df_filtrado['valor_atual_acoes'].iloc[-1]
        capital_disponivel = df_filtrado['capital_disponivel'].iloc[-1]
        st.metric(
            label="🏦 Capital Disponível",
            value=f"R$ {capital_disponivel:,.2f}",
            delta=f"Ações: R$ {valor_acoes:,.2f}"
        )

    # Gráfico principal - Evolução do Capital
    st.header("📈 Evolução Temporal do Capital")

    fig_capital = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Evolução do Capital Total', 'Rendimento Percentual'),
        vertical_spacing=0.1,
        row_heights=[0.7, 0.3]
    )

    # Gráfico 1: Evolução do capital
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_inicial'],
            mode='lines',
            name='Capital Inicial',
            line=dict(color='black', dash='dash'),
            hovertemplate='<b>Capital Inicial</b><br>Data: %{x}<br>Valor: R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_disponivel'],
            mode='lines+markers',
            name='Capital Disponível',
            line=dict(color='blue', width=2),
            marker=dict(size=4),
            hovertemplate='<b>Capital Disponível</b><br>Data: %{x}<br>Valor: R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['valor_atual_acoes'],
            mode='lines+markers',
            name='Valor das Ações',
            line=dict(color='orange', width=2),
            marker=dict(size=4, symbol='square'),
            hovertemplate='<b>Valor das Ações</b><br>Data: %{x}<br>Valor: R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['capital_total'],
            mode='lines+markers',
            name='Capital Total',
            line=dict(color='green', width=3),
            marker=dict(size=6, symbol='diamond'),
            hovertemplate='<b>Capital Total</b><br>Data: %{x}<br>Valor: R$ %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    # Área preenchida entre capital inicial e total
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'].tolist() + df_filtrado['data'].tolist()[::-1],
            y=df_filtrado['capital_inicial'].tolist() + df_filtrado['capital_total'].tolist()[::-1],
            fill='toself',
            fillcolor='rgba(0,255,0,0.1)' if df_filtrado['capital_total'].iloc[-1] > df_filtrado['capital_inicial'].iloc[0] else 'rgba(255,0,0,0.1)',
            line=dict(color='rgba(255,255,255,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=1, col=1
    )

    # Gráfico 2: Rendimento percentual
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['rendimento_capital_percentual'],
            mode='lines+markers',
            name='Rendimento %',
            line=dict(color='purple', width=2),
            marker=dict(size=4, symbol='diamond'),
            hovertemplate='<b>Rendimento</b><br>Data: %{x}<br>Valor: %{y:.2f}%<extra></extra>'
        ),
        row=2, col=1
    )

    # Linha zero no gráfico de rendimento
    fig_capital.add_hline(y=0, line_dash="dash", line_color="black", opacity=0.5, row=2, col=1)

    # Área preenchida no rendimento
    fig_capital.add_trace(
        go.Scatter(
            x=df_filtrado['data'],
            y=df_filtrado['rendimento_capital_percentual'],
            fill='tozeroy',
            fillcolor='rgba(0,255,0,0.2)' if df_filtrado['rendimento_capital_percentual'].iloc[-1] >= 0 else 'rgba(255,0,0,0.2)',
            line=dict(color='rgba(255,255,255,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=2, col=1
    )

    fig_capital.update_layout(
        height=700,
        title_text="Evolução Temporal da Carteira",
        showlegend=True,
        legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
        hovermode='x unified'
    )

    fig_capital.update_xaxes(title_text="Data", row=2, col=1)
    fig_capital.update_yaxes(title_text="Valor (R$)", row=1, col=1)
    fig_capital.update_yaxes(title_text="Rendimento (%)", row=2, col=1)

    st.plotly_chart(fig_capital, use_container_width=True)

    # Análise de transações
    st.header("💼 Análise de Transações")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📋 Resumo de Transações")

        # Agrupar transações por tipo
        compras = carteira[carteira['quantidade'] > 0]
        vendas = carteira[carteira['quantidade'] < 0]

        total_compras = len(compras)
        total_vendas = len(vendas)
        valor_total_compras = (compras['quantidade'] * compras['preco_compra']).sum()
        valor_total_vendas = abs((vendas['quantidade'] * vendas['preco_compra']).sum())

        st.metric("🛒 Total de Compras", total_compras, f"R$ {valor_total_compras:,.2f}")
        st.metric("💰 Total de Vendas", total_vendas, f"R$ {valor_total_vendas:,.2f}")

        # Ações mais negociadas
        st.subheader("🏆 Ações Mais Negociadas")
        acoes_negociadas = carteira.groupby('ticker').agg({
            'quantidade': 'sum',
            'preco_compra': 'mean'
        }).round(2)
        acoes_negociadas = acoes_negociadas[acoes_negociadas['quantidade'] > 0].sort_values('quantidade', ascending=False)
        st.dataframe(acoes_negociadas.head(10))

    with col2:
        st.subheader("📊 Distribuição por Ação")

        # Gráfico de pizza das posições atuais
        posicoes_atuais = carteira.groupby('ticker')['quantidade'].sum()
        posicoes_atuais = posicoes_atuais[posicoes_atuais > 0]

        if not posicoes_atuais.empty:
            fig_pizza = px.pie(
                values=posicoes_atuais.values,
                names=[ticker.replace('.SA', '') for ticker in posicoes_atuais.index],
                title="Distribuição de Ações na Carteira (Quantidade)"
            )
            fig_pizza.update_traces(textposition='inside', textinfo='percent+label')
            st.plotly_chart(fig_pizza, use_container_width=True)
        else:
            st.info("Nenhuma posição atual encontrada.")

    # Timeline de transações
    st.header("⏰ Timeline de Transações")

    # Preparar dados para timeline
    timeline_data = carteira.copy()
    timeline_data['tipo'] = timeline_data['quantidade'].apply(lambda x: 'Compra' if x > 0 else 'Venda')
    timeline_data['valor_transacao'] = timeline_data['quantidade'] * timeline_data['preco_compra']
    timeline_data['ticker_clean'] = timeline_data['ticker'].str.replace('.SA', '')

    fig_timeline = px.scatter(
        timeline_data,
        x='data_compra',
        y='ticker_clean',
        size=abs(timeline_data['valor_transacao']),
        color='tipo',
        hover_data=['quantidade', 'preco_compra', 'valor_transacao'],
        title="Timeline de Transações por Ação",
        color_discrete_map={'Compra': 'green', 'Venda': 'red'}
    )

    fig_timeline.update_layout(
        height=400,
        xaxis_title="Data da Transação",
        yaxis_title="Ação",
        showlegend=True
    )

    st.plotly_chart(fig_timeline, use_container_width=True)

    # Tabela detalhada
    st.header("📋 Dados Detalhados")

    tab1, tab2 = st.tabs(["📈 Evolução Temporal", "💼 Transações"])

    with tab1:
        st.subheader("Dados da Evolução Temporal")

        # Formatar dados para exibição
        df_display = df_filtrado.copy()
        df_display['data'] = df_display['data'].dt.strftime('%d/%m/%Y')

        # Formatar colunas monetárias
        colunas_monetarias = ['capital_inicial', 'capital_disponivel', 'valor_atual_acoes',
                             'valor_total_recuperavel', 'capital_total', 'valor_investido_bruto',
                             'valor_vendido_total', 'rendimento_capital_absoluto']

        for col in colunas_monetarias:
            if col in df_display.columns:
                df_display[col] = df_display[col].apply(lambda x: f"R$ {x:,.2f}")

        # Formatar coluna percentual
        if 'rendimento_capital_percentual' in df_display.columns:
            df_display['rendimento_capital_percentual'] = df_display['rendimento_capital_percentual'].apply(lambda x: f"{x:.2f}%")

        st.dataframe(df_display, use_container_width=True)

    with tab2:
        st.subheader("Histórico de Transações")

        # Formatar dados de transações
        transacoes_display = carteira.copy()
        transacoes_display['data_compra'] = transacoes_display['data_compra'].dt.strftime('%d/%m/%Y')
        transacoes_display['ticker'] = transacoes_display['ticker'].str.replace('.SA', '')
        transacoes_display['preco_compra'] = transacoes_display['preco_compra'].apply(lambda x: f"R$ {x:.2f}")
        transacoes_display['valor_total'] = (carteira['quantidade'] * carteira['preco_compra']).apply(lambda x: f"R$ {x:.2f}")
        transacoes_display['tipo'] = carteira['quantidade'].apply(lambda x: '🛒 Compra' if x > 0 else '💰 Venda')

        # Reordenar colunas
        colunas_ordem = ['data_compra', 'ticker', 'tipo', 'quantidade', 'preco_compra', 'valor_total']
        transacoes_display = transacoes_display[colunas_ordem]

        # Renomear colunas
        transacoes_display.columns = ['Data', 'Ação', 'Tipo', 'Quantidade', 'Preço', 'Valor Total']

        st.dataframe(transacoes_display.sort_values('Data', ascending=False), use_container_width=True)

    # Botão para atualizar dados
    st.sidebar.markdown("---")
    if st.sidebar.button("🔄 Atualizar Análise"):
        st.cache_data.clear()
        with st.spinner("Atualizando análise temporal..."):
            main()
        st.success("✅ Análise atualizada com sucesso!")
        st.rerun()

    # Informações adicionais
    st.sidebar.markdown("---")
    st.sidebar.info(
        "💡 **Dicas:**\n"
        "- Use os filtros de data para focar em períodos específicos\n"
        "- Passe o mouse sobre os gráficos para ver detalhes\n"
        "- A análise é atualizada automaticamente quando você executa o script principal"
    )

if __name__ == "__main__":
    import sys

    # Verificar se deve executar o dashboard
    if len(sys.argv) > 1 and sys.argv[1] == "--dashboard":
        criar_dashboard()
    else:
        main()
